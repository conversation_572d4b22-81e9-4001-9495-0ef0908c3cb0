import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";
import { PortfolioManager } from "./portfolio-manager.ts";
import { DataManager } from "./data-manager.ts";
import { AnalyticsEngine } from "./analytics-engine.ts";
import { RateLimiter } from "./rate-limiter.ts";
import { HistoricalAnalyzer } from "./historical-analyzer.ts";
import { WeightCalculator } from "./weight-calculator.ts";
import { CorrelationEngine } from "./correlation-engine.ts";
import { ProbabilityCalculator } from "./probability-calculator.ts";

const bybitClient = new RestClientV5({
  key: bybit.apiKey,
  secret: bybit.apiSecret,
  testnet: bybit.demo,
  demoTrading: bybit.demo,
  parseAPIRateLimits: true,
});

const rateLimiter = new RateLimiter({
  requestsPerSecond: 15,
  requestsPerMinute: 120,
  burstLimit: 8,
  useAPIRateLimit: true
});

const dataManager = new DataManager(bybitClient, rateLimiter);
const analyticsEngine = new AnalyticsEngine(dataManager);
const portfolioManager = new PortfolioManager(bybitClient, analyticsEngine, rateLimiter);

const main = async () => {
  console.log("🚀 Starting Advanced Bybit Market-Neutral Trading System...");

  try {
    // Initialize the system
    console.log("� Initializing system...");
    await dataManager.initialize();
    await analyticsEngine.initialize();
    await portfolioManager.initialize();

    // Start the main trading loop
    console.log("� Starting market-neutral portfolio management...");
    await portfolioManager.startRebalancing();

  } catch (error) {
    console.error("❌ Error in main execution:", error);
  }
};

// Handle graceful shutdown
const shutdown = async () => {
  console.log("\n� Shutting down gracefully...");
  await portfolioManager.stop();
  Deno.exit(0);
};

// Listen for shutdown signals
Deno.addSignalListener("SIGINT", shutdown);
Deno.addSignalListener("SIGTERM", shutdown);

if (import.meta.main) {
  main();
}
