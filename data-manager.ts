import { RestClientV5 } from "bybit-api";
import { RateLimiter } from "./rate-limiter.ts";
import { OHLCV } from "./technical-indicators.ts";
import { SymbolInfo } from "./market-scanner.ts";

export interface HistoricalData {
  symbol: string;
  timeframe: string;
  data: OHLCV[];
  lastUpdate: number;
  metadata: {
    totalCandles: number;
    startTime: number;
    endTime: number;
  };
}

export interface CachedMarketData {
  symbols: SymbolInfo[];
  lastUpdate: number;
}

export interface DataCache {
  historical: Record<string, HistoricalData>;
  market: CachedMarketData;
  weights: Record<string, any>;
  correlations: Record<string, any>;
  probabilities: Record<string, any>;
}

export class DataManager {
  private client: RestClientV5;
  private rateLimiter: RateLimiter;
  private cache: DataCache;
  private readonly dataDir = "./data";
  private readonly cacheFile = "./data/cache.json";
  private readonly timeframe = "60"; // 1 hour
  private readonly maxCandlesPerRequest = 1000;
  private readonly cacheExpiryHours = 1; // Cache expires after 1 hour

  constructor(client: RestClientV5, rateLimiter: RateLimiter) {
    this.client = client;
    this.rateLimiter = rateLimiter;
    this.cache = {
      historical: {},
      market: { symbols: [], lastUpdate: 0 },
      weights: {},
      correlations: {},
      probabilities: {}
    };
  }

  async initialize(): Promise<void> {
    console.log("🔧 Initializing DataManager...");
    
    // Create data directory if it doesn't exist
    try {
      await Deno.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      if (!(error instanceof Deno.errors.AlreadyExists)) {
        throw error;
      }
    }

    // Load existing cache
    await this.loadCache();
    
    // Update market symbols if cache is stale
    if (this.isMarketCacheStale()) {
      await this.updateMarketSymbols();
    }

    console.log("✅ DataManager initialized");
  }

  private async loadCache(): Promise<void> {
    try {
      const cacheData = await Deno.readTextFile(this.cacheFile);
      this.cache = JSON.parse(cacheData);
      console.log("📂 Loaded existing cache");
    } catch (error) {
      console.log("📂 No existing cache found, starting fresh");
      await this.saveCache();
    }
  }

  private async saveCache(): Promise<void> {
    try {
      await Deno.writeTextFile(this.cacheFile, JSON.stringify(this.cache, null, 2));
    } catch (error) {
      console.error("❌ Error saving cache:", error);
    }
  }

  private isMarketCacheStale(): boolean {
    const now = Date.now();
    const cacheAge = now - this.cache.market.lastUpdate;
    return cacheAge > (this.cacheExpiryHours * 60 * 60 * 1000);
  }

  private isHistoricalCacheStale(symbol: string): boolean {
    const historicalData = this.cache.historical[symbol];
    if (!historicalData) return true;
    
    const now = Date.now();
    const cacheAge = now - historicalData.lastUpdate;
    return cacheAge > (this.cacheExpiryHours * 60 * 60 * 1000);
  }

  async updateMarketSymbols(): Promise<void> {
    console.log("🔄 Updating market symbols...");
    
    try {
      await this.rateLimiter.waitIfNeeded();
      const response = await this.client.getInstrumentsInfo({
        category: 'linear',
        limit: 1000
      });

      if (response.result?.list) {
        this.cache.market.symbols = response.result.list
          .filter(instrument => 
            instrument.status === 'Trading' && 
            instrument.quoteCoin === 'USDT'
          )
          .map(instrument => ({
            symbol: instrument.symbol,
            baseCoin: instrument.baseCoin,
            quoteCoin: instrument.quoteCoin,
            status: instrument.status,
            contractType: instrument.contractType || 'LinearPerpetual'
          }));
        
        this.cache.market.lastUpdate = Date.now();
        await this.saveCache();
        
        console.log(`✅ Updated ${this.cache.market.symbols.length} market symbols`);
      }
    } catch (error) {
      console.error("❌ Error updating market symbols:", error);
      throw error;
    }
  }

  async getHistoricalData(symbol: string, daysBack: number = 30): Promise<OHLCV[]> {
    const cacheKey = `${symbol}_${this.timeframe}`;
    
    // Check if we have fresh cached data
    if (!this.isHistoricalCacheStale(symbol) && this.cache.historical[cacheKey]) {
      console.log(`📊 Using cached data for ${symbol}`);
      return this.cache.historical[cacheKey].data;
    }

    console.log(`📈 Fetching historical data for ${symbol}...`);
    
    try {
      const endTime = Date.now();
      const startTime = endTime - (daysBack * 24 * 60 * 60 * 1000);
      const allCandles: OHLCV[] = [];
      
      let currentEndTime = endTime;
      
      // Fetch data in chunks to respect API limits
      while (currentEndTime > startTime && allCandles.length < (daysBack * 24)) {
        await this.rateLimiter.waitIfNeeded();
        
        const response = await this.client.getKline({
          category: 'linear',
          symbol: symbol,
          interval: this.timeframe,
          end: currentEndTime,
          limit: this.maxCandlesPerRequest
        });

        if (!response.result?.list || response.result.list.length === 0) {
          break;
        }

        const candles: OHLCV[] = response.result.list
          .map((kline: string[]) => ({
            timestamp: parseInt(kline[0]),
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5])
          }))
          .filter(candle => candle.timestamp >= startTime);

        allCandles.unshift(...candles.reverse());
        
        // Update currentEndTime for next iteration
        if (candles.length > 0) {
          currentEndTime = Math.min(...candles.map(c => c.timestamp)) - 1;
        } else {
          break;
        }

        // Small delay to be respectful to API
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Remove duplicates and sort by timestamp
      const uniqueCandles = Array.from(
        new Map(allCandles.map(candle => [candle.timestamp, candle])).values()
      ).sort((a, b) => a.timestamp - b.timestamp);

      // Cache the data
      this.cache.historical[cacheKey] = {
        symbol,
        timeframe: this.timeframe,
        data: uniqueCandles,
        lastUpdate: Date.now(),
        metadata: {
          totalCandles: uniqueCandles.length,
          startTime: uniqueCandles[0]?.timestamp || 0,
          endTime: uniqueCandles[uniqueCandles.length - 1]?.timestamp || 0
        }
      };

      await this.saveCache();
      
      console.log(`✅ Fetched ${uniqueCandles.length} candles for ${symbol}`);
      return uniqueCandles;
      
    } catch (error) {
      console.error(`❌ Error fetching historical data for ${symbol}:`, error);
      
      // Return cached data if available, even if stale
      if (this.cache.historical[cacheKey]) {
        console.log(`📊 Falling back to cached data for ${symbol}`);
        return this.cache.historical[cacheKey].data;
      }
      
      return [];
    }
  }

  async getAvailableSymbols(): Promise<SymbolInfo[]> {
    if (this.isMarketCacheStale()) {
      await this.updateMarketSymbols();
    }
    return this.cache.market.symbols;
  }

  async getTopSymbolsByVolume(limit: number = 50): Promise<SymbolInfo[]> {
    const symbols = await this.getAvailableSymbols();
    
    try {
      await this.rateLimiter.waitIfNeeded();
      const tickersResponse = await this.client.getTickers({
        category: 'linear'
      });

      if (!tickersResponse.result?.list) {
        return symbols.slice(0, limit);
      }

      // Sort by 24h volume and return top symbols
      const sortedTickers = tickersResponse.result.list
        .filter(ticker => parseFloat(ticker.volume24h) > 0)
        .sort((a, b) => parseFloat(b.volume24h) - parseFloat(a.volume24h))
        .slice(0, limit);

      return sortedTickers
        .map(ticker => symbols.find(s => s.symbol === ticker.symbol))
        .filter(Boolean) as SymbolInfo[];
        
    } catch (error) {
      console.error("❌ Error getting top symbols by volume:", error);
      return symbols.slice(0, limit);
    }
  }

  // Methods for storing and retrieving analysis results
  async saveWeights(weights: Record<string, any>): Promise<void> {
    this.cache.weights = weights;
    await this.saveCache();
  }

  async getWeights(): Promise<Record<string, any>> {
    return this.cache.weights;
  }

  async saveCorrelations(correlations: Record<string, any>): Promise<void> {
    this.cache.correlations = correlations;
    await this.saveCache();
  }

  async getCorrelations(): Promise<Record<string, any>> {
    return this.cache.correlations;
  }

  async saveProbabilities(probabilities: Record<string, any>): Promise<void> {
    this.cache.probabilities = probabilities;
    await this.saveCache();
  }

  async getProbabilities(): Promise<Record<string, any>> {
    return this.cache.probabilities;
  }

  // Utility method to get cache statistics
  getCacheStats(): any {
    const historicalCount = Object.keys(this.cache.historical).length;
    const symbolCount = this.cache.market.symbols.length;
    const lastMarketUpdate = new Date(this.cache.market.lastUpdate).toISOString();
    
    return {
      historicalDatasets: historicalCount,
      availableSymbols: symbolCount,
      lastMarketUpdate,
      cacheSize: JSON.stringify(this.cache).length
    };
  }
}
